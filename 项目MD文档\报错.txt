C:\Users\<USER>\Desktop\work\flyXxtt2>composer install
Installing dependencies from lock file (including require-dev)
Verifying lock file contents can be installed on current platform.
Your lock file does not contain a compatible set of packages. Please run composer update.

  Problem 1
    - filament/support is locked to version v3.2.6 and an update of this package was not requested.
    - filament/support v3.2.6 requires ext-intl * -> it is missing from your system. Install or enable PHP's intl extension.
  Problem 2
    - laravel/horizon is locked to version v5.24.5 and an update of this package was not requested.
    - laravel/horizon v5.24.5 requires ext-pcntl * -> it is missing from your system. Install or enable PHP's pcntl extension.
  Problem 3
    - league/flysystem-local is locked to version 3.29.0 and an update of this package was not requested.
    - league/flysystem-local 3.29.0 requires ext-fileinfo * -> it is missing from your system. Install or enable PHP's fileinfo extension.
  Problem 4
    - league/mime-type-detection is locked to version 1.16.0 and an update of this package was not requested.
    - league/mime-type-detection 1.16.0 requires ext-fileinfo * -> it is missing from your system. Install or enable PHP's fileinfo extension.
  Problem 5
    - openspout/openspout is locked to version v4.23.0 and an update of this package was not requested.
    - openspout/openspout v4.23.0 requires ext-fileinfo * -> it is missing from your system. Install or enable PHP's fileinfo extension.
  Problem 6
    - spatie/image is locked to version 3.7.4 and an update of this package was not requested.
    - spatie/image 3.7.4 requires ext-exif * -> it is missing from your system. Install or enable PHP's exif extension.
  Problem 7
    - spatie/image-optimizer is locked to version 1.8.0 and an update of this package was not requested.
    - spatie/image-optimizer 1.8.0 requires ext-fileinfo * -> it is missing from your system. Install or enable PHP's fileinfo extension.
  Problem 8
    - spatie/laravel-medialibrary is locked to version 11.11.0 and an update of this package was not requested.
    - spatie/laravel-medialibrary 11.11.0 requires ext-exif * -> it is missing from your system. Install or enable PHP's exif extension.
  Problem 9
    - league/flysystem is locked to version 3.29.1 and an update of this package was not requested.
    - league/flysystem 3.29.1 requires league/flysystem-local ^3.0.0 -> satisfiable by league/flysystem-local[3.29.0].
    - league/flysystem-local 3.29.0 requires ext-fileinfo * -> it is missing from your system. Install or enable PHP's fileinfo extension.

To enable extensions, verify that they are enabled in your .ini files:
    - C:\phpstudy_pro\Extensions\php\php8.2.9nts\php.ini
You can also run `php --ini` in a terminal to see which files are used by PHP in CLI mode.
Alternatively, you can run Composer with `--ignore-platform-req=ext-intl --ignore-platform-req=ext-pcntl --ignore-platform-req=ext-fileinfo --ignore-platform-req=ext-exif` to temporarily ignore these required extensions.

C:\Users\<USER>\Desktop\work\flyXxtt2>